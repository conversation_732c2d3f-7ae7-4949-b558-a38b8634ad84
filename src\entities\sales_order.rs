use std::str::FromStr;

use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, WhereOptions};
use crate::dtos::sales_order::{SalesOrderCreate, SalesOrderResponse, SalesOrderUpdate};
use anyhow::anyhow;
use chrono::Local;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct SalesOrder {
    pub id: Option<RecordId>,
    pub status: Option<String>,
    pub creator_id: Option<RecordId>,
    pub updater_id: Option<RecordId>,
    pub serial: String,
    // 关联合同
    pub contract_id: Option<RecordId>,
    // 关联款项使用批次
    pub repayment_id: Option<RecordId>,
    // 导入批次
    pub import_record: Option<String>,
    // 下单时间
    pub purchase_time: Option<String>,
    // 支付时间
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    // 商品总金额
    pub amount: Decimal,
    // 运输费用
    pub express_fee: Decimal,
    // 总金额
    pub total_payment: Decimal,
    // 发货时间
    pub delivery_time: Option<String>,
    // 签收时间
    pub sign_time: Option<String>,
    // 订单完成时间
    pub complete_time: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for SalesOrder {}
impl Patchable for SalesOrder {}
impl Castable for SalesOrder {}

impl SalesOrder {
    pub async fn response(self) -> SalesOrderResponse {
        let creator_id = self.creator_id.map(|id| id.to_string());
        let updater_id = self.updater_id.map(|id| id.to_string());
        let contract_id = self.contract_id.map(|id| id.to_string());
        let repayment_id = self.repayment_id.map(|id| id.to_string());

        SalesOrderResponse {
            id: self.id.unwrap().to_string(),
            status: self.status,
            creator_id,
            updater_id,
            contract_id,
            repayment_id,
            serial: self.serial,
            import_record: self.import_record,
            purchase_time: self.purchase_time,
            pay_time: self.pay_time,
            pay_type: self.pay_type,
            pay_info: self.pay_info,
            customer: self.customer,
            receive_phone: self.receive_phone,
            customer_phone: self.customer_phone,
            address: self.address,
            express_type: self.express_type,
            express_company: self.express_company,
            express_order: self.express_order,
            platform_name: self.platform_name,
            platform_serial: self.platform_serial,
            platform_order_serial: self.platform_order_serial,
            platform_fee_total: self.platform_fee_total,
            amount: self.amount,
            express_fee: self.express_fee,
            total_payment: self.total_payment,
            delivery_time: self.delivery_time,
            sign_time: self.sign_time,
            complete_time: self.complete_time,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(sales_order: SalesOrderCreate) -> SalesOrder {
        let time_now = Local::now().timestamp_millis();
        let creator_id = sales_order
            .creator_id
            .map(|id| RecordId::from_str(&id).unwrap());
        let updater_id = sales_order
            .updater_id
            .map(|id| RecordId::from_str(&id).unwrap());

        SalesOrder {
            id: None,
            status: sales_order.status,
            creator_id,
            updater_id,
            contract_id: sales_order
                .contract_id
                .map(|id| RecordId::from_str(&id).unwrap()),
            repayment_id: sales_order
                .repayment_id
                .map(|id| RecordId::from_str(&id).unwrap()),
            serial: sales_order.serial,
            import_record: sales_order.import_record,
            purchase_time: sales_order.purchase_time,
            pay_time: sales_order.pay_time,
            pay_type: sales_order.pay_type,
            pay_info: sales_order.pay_info,
            customer: sales_order.customer,
            receive_phone: sales_order.receive_phone,
            customer_phone: sales_order.customer_phone,
            address: sales_order.address,
            express_type: sales_order.express_type,
            express_company: sales_order.express_company,
            express_order: sales_order.express_order,
            platform_name: sales_order.platform_name,
            platform_serial: sales_order.platform_serial,
            platform_order_serial: sales_order.platform_order_serial,
            platform_fee_total: sales_order.platform_fee_total,
            amount: sales_order.amount,
            express_fee: sales_order.express_fee,
            total_payment: sales_order.total_payment,
            delivery_time: sales_order.delivery_time,
            sign_time: sales_order.sign_time,
            complete_time: sales_order.complete_time,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: SalesOrderUpdate, old: SalesOrder) -> SalesOrder {
        let time_now = Local::now().timestamp_millis();
        let creator_id = new.creator_id.map(|id| RecordId::from_str(&id).unwrap());
        let updater_id = new.updater_id.map(|id| RecordId::from_str(&id).unwrap());

        SalesOrder {
            id: old.id.clone(),
            status: new.status,
            creator_id,
            updater_id,
            contract_id: new.contract_id.map(|id| RecordId::from_str(&id).unwrap()),
            repayment_id: new.repayment_id.map(|id| RecordId::from_str(&id).unwrap()),
            serial: new.serial,
            import_record: new.import_record,
            purchase_time: new.purchase_time,
            pay_time: new.pay_time,
            pay_type: new.pay_type,
            pay_info: new.pay_info,
            customer: new.customer,
            receive_phone: new.receive_phone,
            customer_phone: new.customer_phone,
            address: new.address,
            express_type: new.express_type,
            express_company: new.express_company,
            express_order: new.express_order,
            platform_name: new.platform_name,
            platform_serial: new.platform_serial,
            platform_order_serial: new.platform_order_serial,
            platform_fee_total: new.platform_fee_total,
            amount: new.amount,
            express_fee: new.express_fee,
            total_payment: new.total_payment,
            delivery_time: new.delivery_time,
            sign_time: new.sign_time,
            complete_time: new.complete_time,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

pub struct SalesOrderBmc;

impl SalesOrderBmc {
    const ENTITY: &'static str = "sales_order";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<SalesOrder>> {
        println!("传入参数：：：{:?}, {:?}", page, limit);
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<SalesOrder>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<SalesOrder>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(sales_order: SalesOrderCreate) -> AppResult<String> {
        let obj = SalesOrder::create(sales_order);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn create_or_update(sales_order: SalesOrderCreate) -> AppResult<String> {
        if sales_order.platform_order_serial.is_none() {
            return Err(anyhow!("平台订单号缺失，无法导入.").into());
        }
        let check_params = vec![WhereOptions {
            var: "platform_order_serial".to_string(),
            val: sales_order.platform_order_serial.clone().unwrap(),
        }];
        let check: Option<SalesOrder> =
            Database::exec_get_by_query(Self::ENTITY, check_params).await?;
        if check.is_none() {
            let obj = SalesOrder::create(sales_order);
            let res = Database::exec_create(Self::ENTITY, obj).await?;
            return Ok(res);
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        Database::exec_update(Self::ENTITY, &final_tid, sales_order).await
    }

    pub async fn update(sales_order: SalesOrderUpdate) -> AppResult<String> {
        let check: Option<SalesOrder> =
            Database::exec_get_by_id(Self::ENTITY, &sales_order.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("SalesOrder not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = SalesOrder::update(sales_order, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    /// 根据查询条件更新单个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let result = SalesOrderBmc::update_field(params, "status", "completed").await?;
    /// ```
    pub async fn update_field(
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        Database::exec_update_by_query(Self::ENTITY, params, update_field, update_value).await
    }
}
