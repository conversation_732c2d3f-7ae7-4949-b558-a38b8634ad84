use crate::app_writer::AppResult;
use crate::config::CFG;
use salvo::prelude::{Extractible, ToSchema};
use serde::de::{self, Deserializer, Visitor};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use std::fmt;
use std::sync::LazyLock;
use surrealdb::engine::remote::ws::{Client, Ws};
use surrealdb::opt::auth::Root;
use surrealdb::{sql::Thing, Surreal};

const PAGE_LIMIT: u32 = 15;

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct Page {
    pub limit: u32,
    pub page: u32,
}

impl Page {
    pub fn default() -> Self {
        Page {
            limit: PAGE_LIMIT,
            page: 0,
        }
    }

    pub fn unlimited() -> Self {
        Page { limit: 0, page: 0 }
    }

    pub fn get_limit(&self) -> anyhow::Result<u32> {
        if self.limit < 0 {
            return Err(anyhow::anyhow!("page limit must be greater than 0"));
        }
        Ok(self.limit)
    }

    pub fn get_offset(&self) -> u32 {
        if self.page == 0 {
            return 0;
        }
        (self.page - 1) * self.limit
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct FlexibleParams {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub options: Option<ListOptions>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<IdParams>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub params: Option<Vec<WhereOptions>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub page: Option<Page>,
}

// 为FlexibleParams实现默认值
impl Default for FlexibleParams {
    fn default() -> Self {
        Self {
            options: None,
            id: None,
            params: None,
            page: None,
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct ListOptions {
    pub order_by: Option<String>,
    pub desc: Option<bool>,
}

impl ListOptions {
    pub fn default() -> Self {
        ListOptions {
            order_by: Some("created_at".to_string()),
            desc: Some(true),
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct WhereOptions {
    pub var: String,
    pub val: String,
}

impl WhereOptions {
    pub fn get_sql(&self) -> String {
        match self.var.as_str() {
            "id" => format!("{} = {}", self.var, self.val),
            "ids" => format!("id IN [{}]", self.val),
            "owner" => format!("owner = {}", self.val),
            "sender" => format!("sender = {}", self.val),
            "receiver" => format!("receiver = {}", self.val),
            "user" => format!("user = {}", self.val),
            "users" => format!("user IN [{}]", self.val),
            "role" => format!("role = {}", self.val),
            "group" => format!("group = {}", self.val),
            "roles" => format!("role IN [{}]", self.val),
            "groups" => format!("group IN [{}]", self.val),
            "usage" => format!("usage = {}", self.val),
            "usages" => format!("usage IN [{}]", self.val),
            "tag" => format!("tag = {}", self.val),
            "tags" => format!("tag IN [{}]", self.val),
            "activity" => format!("activity = {}", self.val),
            "activities" => format!("activity IN [{}]", self.val),
            "begin_date" => format!("date >= '{}'", self.val),
            "end_date" => format!("date <= '{}'", self.val),
            "ralate" | "raw" => self.val.clone(),
            _ if self.var.contains(".id") || self.var.contains("_id") => {
                format!("{} = {}", self.var, self.val)
            }
            _ => format!("{} = '{}'", self.var, self.val),
        }
    }

    pub fn new(var: String, val: String) -> Self {
        WhereOptions { var, val }
    }
}

/// 字段更新选项，用于多字段更新
#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct UpdateOptions {
    pub field: String,
    pub value: String,
}

impl UpdateOptions {
    /// 生成SQL SET子句
    pub fn get_sql(&self) -> String {
        match self.field.as_str() {
            // ID字段不需要引号
            _ if self.field.contains(".id") || self.field.contains("_id") => {
                format!("{} = {}", self.field, self.value)
            }
            // 数值字段不需要引号
            "amount" | "express_fee" | "total_payment" | "platform_fee_total" |
            "sales_price" | "cost_price" | "platform_fee" | "discount" | "quantity" |
            "total_sales_price" | "total_cost_price" | "created_at" | "updated_at" => {
                format!("{} = {}", self.field, self.value)
            }
            // 其他字段需要引号
            _ => format!("{} = '{}'", self.field, self.value),
        }
    }

    pub fn new(field: String, value: String) -> Self {
        UpdateOptions { field, value }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct IdParams {
    pub id: String,
}

#[derive(Deserialize, Debug)]
pub struct CreateParams<D> {
    pub data: D,
}

#[derive(Deserialize, Debug)]
pub struct UpdateParams<D> {
    pub data: D,
}

#[derive(Deserialize, Debug, Clone, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RelateParams {
    pub from: String,
    pub to: Vec<String>,
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ListParams {
    pub page: Option<Page>,
    pub options: Option<ListOptions>,
    pub params: Option<Vec<WhereOptions>>,
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct MessageListParams {
    pub page: Option<Page>,
    pub options: Option<ListOptions>,
    pub req_type: String,
}

#[derive(Default, Serialize, Debug, ToSchema)]
pub struct ListResponse<T: Serialize> {
    pub data: Vec<T>,
    pub total: u32,
    pub page: u32,
    pub size: u32,
}

static GDB: LazyLock<Surreal<Client>> = LazyLock::new(Surreal::init);

pub async fn init_db() -> Result<(), surrealdb::Error> {
    let host = format!("{}:{}", &CFG.database.url, &CFG.database.port);
    let username = &CFG.database.username;
    let password = &CFG.database.password;
    let ns = &CFG.database.db_ns;
    let db_name = &CFG.database.db_name;

    println!("数据库连接信息：{}", host);

    GDB.connect::<Ws>(host).await?;

    GDB.signin(Root {
        username: &username,
        password: &password,
    })
    .await?;

    GDB.use_ns(ns).use_db(db_name).await.unwrap();

    Ok(())
}

pub trait Castable: DeserializeOwned {}
pub trait Creatable: Serialize {}
pub trait Patchable: Serialize {}

#[derive(Debug, Deserialize, Serialize)]
pub struct Record {
    pub id: Thing,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CountRecord {
    pub count: u32,
}

#[derive(Debug, Default, Deserialize, Serialize, Clone)]
pub struct SumForChart {
    #[serde(deserialize_with = "flexible_string_deserializer")]
    pub field: String,
    #[serde(deserialize_with = "flexible_string_deserializer")]
    pub label: String,
    pub value: f64,
}

fn flexible_string_deserializer<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: Deserializer<'de>,
{
    struct FlexibleStringVisitor;

    impl<'de> Visitor<'de> for FlexibleStringVisitor {
        type Value = String;

        fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
            formatter.write_str("string, integer, Thing, or map")
        }

        fn visit_str<E: de::Error>(self, v: &str) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_i64<E: de::Error>(self, v: i64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_u64<E: de::Error>(self, v: u64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_f64<E: de::Error>(self, v: f64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }
    }

    deserializer.deserialize_any(FlexibleStringVisitor)
}
pub struct Database;

#[allow(dead_code)]
impl Database {
    /// 清空数据库中的所有数据
    ///
    /// # 参数
    /// * `table` - 要清空的表名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_clean_db(table: &str) -> AppResult<String> {
        let sql = format!("DELETE {} RETURN NONE;", table);
        GDB.query(sql).await?;
        Ok(format!("表 {} 已清空", table))
    }
    pub async fn exec_query_count(
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<CountRecord>> {
        let mut sql = format!("SELECT COUNT() FROM {}", table);
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                // sql.push_str(&format!("{} = '{}'", param.var, param.val));
                sql.push_str(param.get_sql().as_str());
            }
        }
        sql.push_str(" GROUP ALL;");
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }

    pub async fn exec_query_list<T: Castable>(
        table: &str,
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<T>> {
        let mut sql = format!("SELECT * FROM {} ", table);

        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                // sql.push_str(&format!("{} = '{}'", param.var, param.val));
                sql.push_str(param.get_sql().as_str());
            }
        }

        if let Some(options) = options {
            sql.push_str(" ORDER BY ");

            if let Some(order_by) = options.order_by {
                sql.push_str(&order_by);
            }

            match options.desc {
                Some(true) => sql.push_str(" DESC"),
                Some(false) => sql.push_str(" ASC"),
                None => sql.push_str(" ASC"),
            }
        }

        if limit > 0 {
            sql.push_str(&format!(" LIMIT {} START {}", limit, page));
        };

        sql.push_str(" ;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;

        let res = response.take(0)?;
        Ok(res)
    }

    pub async fn exec_get_by_id<T: Castable>(table: &str, tid: &str) -> AppResult<Option<T>> {
        let parts: Vec<&str> = tid.split(':').collect();
        if parts.len() == 2 {
            let res = GDB.select((table, parts[1])).await?;
            return Ok(res);
        }
        let res = GDB.select((table, tid)).await?;
        Ok(res)
    }

    pub async fn exec_get_by_query<T: Castable>(
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<T>> {
        let mut sql = format!("SELECT * FROM {} WHERE ", table);
        for (i, param) in params.iter().enumerate() {
            if i > 0 {
                sql.push_str(" AND ");
            }
            // sql.push_str(&format!("{} = '{}'", param.var, param.val));
            sql.push_str(param.get_sql().as_str());
        }

        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);
        let mut ress = GDB.query(sql).await?;
        let res = ress.take(0)?;
        Ok(res)
    }

    pub async fn exec_get_field_by_query<T: Castable>(
        field: &str,
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<T>> {
        let mut sql = format!("SELECT {} FROM {} WHERE ", field, table);
        for (i, param) in params.iter().enumerate() {
            if i > 0 {
                sql.push_str(" AND ");
            }
            // sql.push_str(&format!("{} = '{}'", param.var, param.val));
            sql.push_str(param.get_sql().as_str());
        }

        sql.push_str(" LIMIT 1;");
        let mut ress = GDB.query(sql).await?;
        let res = ress.take(0)?;
        Ok(res)
    }

    pub async fn exec_create<T: Creatable + 'static>(table: &str, obj: T) -> AppResult<String> {
        let ress: Option<Record> = GDB.create(table).content(obj).await?;
        match ress {
            Some(res) => Ok(res.id.to_raw()),
            None => Err(anyhow::anyhow!("create failed").into()),
        }
    }

    pub async fn exec_update<T: Patchable + 'static>(
        table: &str,
        tid: &str,
        obj: T,
    ) -> AppResult<String> {
        let final_tid = if tid.to_string().contains(':') {
            tid.to_string()
                .split(':')
                .nth(1)
                .unwrap_or(&tid)
                .to_string()
        } else {
            tid.to_string().clone()
        };
        let res: Option<Record> = GDB.update((table, &final_tid)).merge(obj).await?;
        match res {
            Some(_) => Ok(tid.to_string()),
            None => Err(anyhow::anyhow!("update failed, table: {}, tid: {}", table, tid).into()),
        }
    }

    pub async fn exec_delete(table: &str, tid: String) -> AppResult<String> {
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };

        let res: Option<Record> = GDB.delete((table, &final_tid)).await?;
        match res {
            Some(_) => Ok(tid),
            None => Err(anyhow::anyhow!("delete failed, table: {}, tid: {}", table, tid).into()),
        }
    }

    // from -> table -> to 对应 in -> table -> out的结构
    pub async fn exec_relate(table: &str, from: &str, to: &str) -> AppResult<String> {
        let sql = format!("RELATE {}->{}->{};", from, table, to);
        let res: Option<Record> = GDB.query(sql).await?.take(0)?;
        match res {
            Some(_) => Ok(format!("{from} -> {table} -> {to}")),
            None => Err(anyhow::anyhow!(
                "relate failed, table: {}, from: {}, to: {}",
                table,
                from,
                to
            )
            .into()),
        }
    }

    // from -> table -> to 对应 in -> table -> out的结构
    pub async fn exec_relate_batch(
        table: &str,
        from: Vec<String>,
        to: Vec<String>,
    ) -> AppResult<String> {
        let from_id = if from.len() == 1 {
            from[0].clone()
        } else {
            format!("[{}]", from.join(","))
        };

        let to_id = if to.len() == 1 {
            to[0].clone()
        } else {
            format!("[{}]", to.join(","))
        };
        let sql = format!("RELATE {}->{}->{};", from_id, table, to_id);
        GDB.query(sql).await?;
        Ok("Items has been related !".to_string())
    }

    pub async fn exec_query_relate<T: Castable>(
        table: &str,
        page: u32,
        limit: u32,
        state: &str,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<T>> {
        let mut sql = format!("SELECT *, {} FROM {} ", state, table);
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                // sql.push_str(&format!("{} = '{}'", param.var, param.val));
                sql.push_str(param.get_sql().as_str());
            }
        }

        if let Some(options) = options {
            sql.push_str(" ORDER BY ");

            if let Some(order_by) = options.order_by {
                sql.push_str(&order_by);
            } else {
                sql.push_str("create_at");
            }

            if let Some(_desc) = options.desc {
                sql.push_str(" DESC");
            } else {
                sql.push_str(" ASC");
            }
        }

        if limit > 0 {
            sql.push_str(&format!(" LIMIT {} START {}", limit, page));
        };

        sql.push_str(" ;");
        println!("SQL语句：{}", sql);
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }

    pub async fn exec_return_relate(table: &str, from: &str, to: &str) -> AppResult<Vec<String>> {
        let sql = format!("RETURN {}->{}->{}", from, table, to);
        let mut response = GDB.query(sql).await?;
        let list: Vec<Thing> = response.take(0)?;
        let mut res = Vec::new();
        for item in list {
            res.push(item.to_raw());
        }
        Ok(res)
    }

    pub async fn exec_unrelate(table: &str, from: &str, to: &str) -> AppResult<String> {
        let sql = format!("DELETE {from}->{table} WHERE out={to} RETURN BEFORE;");
        let res: Option<Record> = GDB.query(sql).await?.take(0)?;
        match res {
            Some(_) => Ok(format!("{from} -> {table} -> {to}")),
            None => Err(anyhow::anyhow!(
                "delete relate failed, table: {}, from: {}, to: {}",
                table,
                from,
                to
            )
            .into()),
        }
    }

    /// 根据查询条件更新单条记录
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_update_by_query(
        table: &str,
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        let mut sql = format!("UPDATE {} SET {} = '{}'", table, update_field, update_value);
        
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }
        
        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);
        
        let mut response = GDB.query(sql).await?;
        let res: Option<Record> = response.take(0)?;
        
        match res {
            Some(record) => Ok(record.id.to_raw()),
            None => Err(anyhow::anyhow!("update by query failed, table: {}, field: {}", table, update_field).into()),
        }
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_update_multiple_fields_by_query(
        table: &str,
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        if update_fields.is_empty() {
            return Err(anyhow::anyhow!("update fields cannot be empty").into());
        }

        let mut sql = format!("UPDATE {} SET ", table);

        // 构建SET子句
        for (i, field) in update_fields.iter().enumerate() {
            if i > 0 {
                sql.push_str(", ");
            }
            sql.push_str(&field.get_sql());
        }

        // 构建WHERE子句
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let res: Option<Record> = response.take(0)?;

        match res {
            Some(record) => Ok(record.id.to_raw()),
            None => Err(anyhow::anyhow!("update multiple fields by query failed, table: {}", table).into()),
        }
    }

    // 函数限制：必须按年份查询，固定输入年份
    pub async fn exec_summary_by_month(
        table: &str,
        state: &str,
        label: &str,
        year: i32,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<SumForChart>> {
        let mut sql = format!(
            "
            SELECT 
                {} AS field, 
                {} AS label,
                math::sum(amount) AS value 
            FROM {} 
            WHERE time::year(<datetime>date) = {}",
            state, label, table, year
        );
        if params.len() > 0 {
            sql.push_str(" AND ");
            for param in params.iter() {
                sql.push_str(param.get_sql().as_str());
            }
        }
        sql.push_str(
            "
            GROUP BY 
                field, label
            ORDER BY 
                field, label ASC;",
        );
        println!("SQL语句：{}", sql);
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }
}
