use anyhow::anyhow;
use rust_decimal::Decimal;

use crate::{
    app_writer::AppResult,
    db::{ListParams, UpdateOptions, WhereOptions},
    dtos::sales_order::{SalesOrderCreate, SalesOrderSummary, SalesOrderUpdate},
    entities::sales_order::{SalesOrder, SalesOrderBmc},
};

pub struct SalesOrderService;
impl SalesOrderService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match SalesOrderBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<SalesOrder>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = SalesOrderBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<SalesOrder> {
        match SalesOrderBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("SalesOrder not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<SalesOrder> {
        match SalesOrderBmc::get_by_id(id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("SalesOrder not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn create(req: SalesOrderCreate) -> AppResult<String> {
        SalesOrderBmc::create(req).await?;
        Ok("SalesOrder created".to_string())
    }

    pub async fn update(req: SalesOrderUpdate) -> AppResult<String> {
        SalesOrderBmc::update(req).await?;
        Ok("SalesOrder updated".to_string())
    }

    /// 优化版本的更新方法，使用多字段更新功能
    ///
    /// # 参数
    /// * `req` - 要更新的销售订单数据
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    ///
    /// # 优势
    /// - 使用单次SQL操作更新所有字段
    /// - 避免了完整对象的序列化和反序列化
    /// - 提高了性能，特别是在网络延迟较高的环境中
    pub async fn update_optimized(req: SalesOrderUpdate) -> AppResult<String> {
        SalesOrderBmc::update_optimized(req).await?;
        Ok("SalesOrder updated (optimized)".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        SalesOrderBmc::delete(id).await?;
        Ok("SalesOrder deleted".to_string())
    }

    /// 统计销售订单的支付汇总信息
    /// # 参数
    /// * `params` - 查询条件参数
    /// # 返回
    /// * `AppResult<SalesOrderSummary>` - 包含总金额、未支付金额和已支付金额的统计结果
    pub async fn count_total_payment(params: Vec<WhereOptions>) -> AppResult<SalesOrderSummary> {
        let list = SalesOrderBmc::get_list(0, 0, None, params).await?;
        
        // 使用单次循环同时计算三个统计值
        let (total_amount, unpay_amount, paid_amount) = list.iter().fold(
            (Decimal::ZERO, Decimal::ZERO, Decimal::ZERO),
            |(total, unpaid, paid), order| {
                let amount = order.total_payment;
                if order.repayment_id.is_none() {
                    // 未支付订单
                    (total + amount, unpaid + amount, paid)
                } else {
                    // 已支付订单
                    (total + amount, unpaid, paid + amount)
                }
            },
        );
        
        Ok(SalesOrderSummary {
            total_amount,
            unpay_amount,
            paid_amount,
        })
    }
    
    /// 根据查询条件更新单个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let result = SalesOrderService::update_field(params, "status", "completed").await?;
    /// ```
    pub async fn update_field(
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        SalesOrderBmc::update_field(params, update_field, update_value).await?;
        Ok("SalesOrder updated".to_string())
    }

    /// 根据查询条件同时更新金额相关字段（amount 和 total_payment）
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `amount` - 新的商品总金额
    /// * `total_payment` - 新的总支付金额
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let result = SalesOrderService::update_amount_fields(params, amount, total_payment).await?;
    /// ```
    pub async fn update_amount_fields(
        params: Vec<WhereOptions>,
        amount: &str,
        total_payment: &str,
    ) -> AppResult<String> {
        // 使用多字段更新功能，一次性更新两个字段
        let update_fields = vec![
            UpdateOptions::new("amount".to_string(), amount.to_string()),
            UpdateOptions::new("total_payment".to_string(), total_payment.to_string()),
        ];
        SalesOrderBmc::update_multiple_fields(params, update_fields).await?;
        Ok("SalesOrder amount fields updated".to_string())
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let update_fields = vec![
    ///     UpdateOptions::new("status".to_string(), "completed".to_string()),
    ///     UpdateOptions::new("amount".to_string(), "1000.00".to_string()),
    /// ];
    /// let result = SalesOrderService::update_multiple_fields(params, update_fields).await?;
    /// ```
    pub async fn update_multiple_fields(
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        SalesOrderBmc::update_multiple_fields(params, update_fields).await?;
        Ok("SalesOrder multiple fields updated".to_string())
    }
}
