# 多字段更新功能优化文档

## 概述

本次优化为数据库操作添加了多字段更新功能，允许在一次SQL操作中更新多个字段，提高了性能并简化了代码。

## 新增功能

### 1. UpdateOptions 结构体

```rust
#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct UpdateOptions {
    pub field: String,
    pub value: String,
}
```

- 用于描述要更新的字段和值
- 提供 `get_sql()` 方法生成SQL SET子句
- 支持ID字段的特殊处理

### 2. exec_update_multiple_fields_by_query 函数

```rust
pub async fn exec_update_multiple_fields_by_query(
    table: &str,
    params: Vec<WhereOptions>,
    update_fields: Vec<UpdateOptions>,
) -> AppResult<String>
```

- 支持一次性更新多个字段
- 生成优化的SQL语句
- 保持与原有函数相同的错误处理机制

### 3. SalesOrderBmc 新增方法

```rust
pub async fn update_multiple_fields(
    params: Vec<WhereOptions>,
    update_fields: Vec<UpdateOptions>,
) -> AppResult<String>
```

### 4. SalesOrderService 新增方法

```rust
// 多字段更新
pub async fn update_multiple_fields(
    params: Vec<WhereOptions>,
    update_fields: Vec<UpdateOptions>,
) -> AppResult<String>

// 优化后的金额字段更新
pub async fn update_amount_fields(
    params: Vec<WhereOptions>,
    amount: &str,
    total_payment: &str,
) -> AppResult<String>
```

## 使用示例

### 单字段更新（原有功能）
```rust
let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
SalesOrderService::update_field(params, "status", "completed").await?;
```

### 多字段更新（新功能）
```rust
let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
let update_fields = vec![
    UpdateOptions::new("status".to_string(), "completed".to_string()),
    UpdateOptions::new("amount".to_string(), "1500.00".to_string()),
    UpdateOptions::new("total_payment".to_string(), "1500.00".to_string()),
];
SalesOrderService::update_multiple_fields(params, update_fields).await?;
```

### 金额字段更新（优化后）
```rust
let params = vec![WhereOptions::new("id".to_string(), "sales_order:123".to_string())];
SalesOrderService::update_amount_fields(params, "2000.00", "2000.00").await?;
```

## SQL生成对比

### 旧方式（多次调用）
```sql
UPDATE sales_order SET amount = '1000.00' WHERE serial = 'SO001' LIMIT 1;
UPDATE sales_order SET total_payment = '1000.00' WHERE serial = 'SO001' LIMIT 1;
```

### 新方式（一次调用）
```sql
UPDATE sales_order SET amount = '1000.00', total_payment = '1000.00' WHERE serial = 'SO001' LIMIT 1;
```

## 性能优势

1. **减少数据库连接次数**：从N次减少到1次
2. **减少网络往返**：单次SQL执行
3. **提高事务一致性**：原子性操作
4. **减少锁竞争**：单次锁定记录

## 在 OrderImportService 中的应用

### 优化前
```rust
// 需要构造完整的 SalesOrderUpdate 对象（42行代码）
let update_data = SalesOrderUpdate {
    id: current_order.id.unwrap().to_string().split(':').nth(1).unwrap_or("").to_string(),
    // ... 所有字段赋值
    amount: updated_amount,
    total_payment: updated_total_payment,
    // ... 更多字段
};
SalesOrderBmc::update(update_data).await?;
```

### 优化后
```rust
// 使用多字段更新，只需13行代码
let order_id = current_order.id.unwrap().to_string();
let query_params = vec![WhereOptions::new("id".to_string(), order_id)];

SalesOrderService::update_amount_fields(
    query_params,
    &updated_amount.to_string(),
    &updated_total_payment.to_string(),
).await?;
```

## 兼容性

- 保持原有 `exec_update_by_query` 函数不变
- 新功能为增量添加，不影响现有代码
- 所有现有的单字段更新调用继续正常工作

## 扩展性

该设计模式可以轻松扩展到其他实体：
- PurchaseOrderBmc
- SalesOrderInfoBmc  
- 其他需要多字段更新的实体

## 总结

通过添加多字段更新功能，我们实现了：
- 代码简化：从42行减少到13行
- 性能提升：减少数据库操作次数
- 维护性提高：专用方法处理特定场景
- 扩展性增强：可复用的设计模式
