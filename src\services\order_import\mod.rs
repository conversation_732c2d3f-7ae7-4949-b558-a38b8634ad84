use crate::app_writer::AppResult;
use crate::dtos::import_log::ImportLogCreate;
use crate::dtos::import_record::ImportRecordCreate;
use crate::dtos::sales_order_info::SalesOrderInfoCreate;
use crate::entities::import_log::ImportLogBmc;
use crate::entities::sales_order::SalesOrderBmc;
use crate::entities::sales_order_info::SalesOrderInfoBmc;
use crate::utils::rand_utils::random_uppercase_serial;
use crate::db::WhereOptions;
use crate::dtos::sales_order::{SalesOrderCreate, SalesOrderUpdate};
use anyhow::anyhow;
use chrono::Local;
pub mod platform_importers;

/// 定义订单导入特征
pub trait OrderImporter {
    type OrderType;

    /// 获取平台名称
    fn get_platform_name(&self) -> String;

    /// 将平台订单转换为销售订单
    fn to_sales_order_create(
        &self,
        order: &Self::OrderType,
    ) -> SalesOrderCreate;

    /// 将平台订单转换为销售订单信息
    fn to_sales_order_info_create(
        &self,
        order: &Self::OrderType,
    ) -> SalesOrderInfoCreate;

    /// 获取订单ID
    fn get_order_id(&self, order: &Self::OrderType) -> String;
}

/// 通用订单导入服务
pub struct OrderImportService;

impl OrderImportService {
    /// 通用导入方法
    pub async fn import<T, I>(orders: Vec<T>, importer: I, import_record_id: Option<String>) -> AppResult<ImportRecordCreate>

    where
        I: OrderImporter<OrderType = T>,
    {
        if orders.is_empty() {
            return Err(anyhow!("导入数据为空").into());
        }

        let mut fail_list = Vec::new();
        let total_count = orders.len() as i64;
        let mut new_count = 0i64;
        let mut update_count = 0i64;
        let mut no_change_count = 0i64;
        let mut success_count = 0i64;
        let mut fail_count = 0i64;
        let mut res = ImportRecordCreate::default();
        
        for order in orders {
            let serial = random_uppercase_serial(Some("SO".to_string()), 8);
            let mut sales_order_create = importer.to_sales_order_create(&order);
            sales_order_create.serial = serial.clone();
            sales_order_create.import_record = import_record_id.clone();

            // 获取订单的 contract_id 和 order_id
            let order_contract_id = sales_order_create.contract_id.clone().unwrap_or_default();
            let order_id = sales_order_create.platform_order_serial.clone().unwrap_or_default();
            
            // 构建查询条件
            let query_params = vec![
                WhereOptions {
                    var: "contract_id".to_string(),
                    val: order_contract_id,
                },
                WhereOptions {
                    var: "platform_order_serial".to_string(),
                    val: order_id.clone(),
                },
            ];
            
            // 查询现有数据
            match SalesOrderBmc::get_by_query(query_params).await {
                Ok(existing_order) => {
                    if let Some(existing) = existing_order {
                        // 存在已有条目，对比 order_status
                        let existing_status = existing.status.unwrap_or_default();
                        let new_status = sales_order_create.status.clone().unwrap_or_default();
                        
                        if existing_status != new_status {
                            // 状态不同，进行更新
                            let update_data = SalesOrderUpdate {
                                id: existing.id.unwrap().to_string().split(':').nth(1).unwrap_or("").to_string(),
                                status: sales_order_create.status,
                                creator_id: sales_order_create.creator_id,
                                updater_id: sales_order_create.updater_id,
                                serial: sales_order_create.serial,
                                contract_id: sales_order_create.contract_id,
                                repayment_id: sales_order_create.repayment_id,
                                import_record: sales_order_create.import_record.clone(),
                                purchase_time: sales_order_create.purchase_time,
                                pay_time: sales_order_create.pay_time,
                                pay_type: sales_order_create.pay_type,
                                pay_info: sales_order_create.pay_info,
                                customer: sales_order_create.customer,
                                receive_phone: sales_order_create.receive_phone,
                                customer_phone: sales_order_create.customer_phone,
                                address: sales_order_create.address,
                                express_type: sales_order_create.express_type,
                                express_company: sales_order_create.express_company,
                                express_order: sales_order_create.express_order,
                                platform_name: sales_order_create.platform_name,
                                platform_serial: sales_order_create.platform_serial,
                                platform_order_serial: sales_order_create.platform_order_serial,
                                platform_fee_total: sales_order_create.platform_fee_total,
                                amount: sales_order_create.amount,
                                express_fee: sales_order_create.express_fee,
                                total_payment: sales_order_create.total_payment,
                                delivery_time: sales_order_create.delivery_time,
                                sign_time: sales_order_create.sign_time,
                                complete_time: sales_order_create.complete_time,
                                created_at: existing.created_at,
                                updated_at: Local::now().timestamp_millis(),
                            };
                            
                            match SalesOrderBmc::update(update_data).await {
                                Ok(_) => {
                                    update_count += 1;
                                    success_count += 1;
                                    
                                }
                                Err(e) => {
                                    fail_count += 1;
                                    let order_id = importer.get_order_id(&order);
                                    fail_list.push(order_id.clone());
                                    let fail_info = ImportLogCreate {
                                        import_id: order_id,
                                        import_source: importer.get_platform_name(),
                                        import_err: format!("更新订单失败: {}", e),
                                        ..Default::default()
                                    };
                                    ImportLogBmc::create(fail_info).await?;
                                }
                            }
                        } else {
                            // 状态相同，通过SalesOrderInfoBmc查询是否当前订单内存在相同的product_serial，如果没有则插入
                            let sales_order_info_create = importer.to_sales_order_info_create(&order);
                            
                            // 构建查询条件：order_serial 和 product_serial
                            let query_params = vec![
                                WhereOptions::new("order_serial".to_string(), sales_order_info_create.order_serial.clone()),
                                WhereOptions::new("product_serial".to_string(), sales_order_info_create.product_serial.clone()),
                            ];
                            
                            // 查询是否存在相同的订单条目
                            match SalesOrderInfoBmc::get_by_query(query_params).await {
                                Ok(existing_info) => {
                                    if existing_info.is_none() {
                                        // 不存在相同的订单条目，创建新的sales_order_info
                                        match SalesOrderInfoBmc::create(sales_order_info_create.clone()).await {
                                            Ok(_) => {
                                                new_count += 1;
                                                success_count += 1;
                                                
                                                // 同步更新 sales_order 的汇总字段
                                                let order_serial = sales_order_info_create.order_serial.clone();
                                                let query_params = vec![WhereOptions::new("serial".to_string(), order_serial)];
                                                
                                                if let Ok(Some(current_order)) = SalesOrderBmc::get_by_query(query_params).await {
                                                    let updated_amount = current_order.amount + sales_order_info_create.total_sales_price;
                                                    let updated_total_payment = current_order.total_payment + sales_order_info_create.total_sales_price;
                                                    
                                                    let update_data = SalesOrderUpdate {
                                                        id: current_order.id.unwrap().to_string().split(':').nth(1).unwrap_or("").to_string(),
                                                        status: current_order.status,
                                                        creator_id: current_order.creator_id.map(|id| id.to_string()),
                                                        updater_id: current_order.updater_id.map(|id| id.to_string()),
                                                        serial: current_order.serial,
                                                        contract_id: current_order.contract_id.map(|id| id.to_string()),
                                                        repayment_id: current_order.repayment_id.map(|id| id.to_string()),
                                                        import_record: current_order.import_record,
                                                        purchase_time: current_order.purchase_time,
                                                        pay_time: current_order.pay_time,
                                                        pay_type: current_order.pay_type,
                                                        pay_info: current_order.pay_info,
                                                        customer: current_order.customer,
                                                        receive_phone: current_order.receive_phone,
                                                        customer_phone: current_order.customer_phone,
                                                        address: current_order.address,
                                                        express_type: current_order.express_type,
                                                        express_company: current_order.express_company,
                                                        express_order: current_order.express_order,
                                                        platform_name: current_order.platform_name,
                                                        platform_serial: current_order.platform_serial,
                                                        platform_order_serial: current_order.platform_order_serial,
                                                        platform_fee_total: current_order.platform_fee_total,
                                                        amount: updated_amount,
                                                        express_fee: current_order.express_fee,
                                                        total_payment: updated_total_payment,
                                                        delivery_time: current_order.delivery_time,
                                                        sign_time: current_order.sign_time,
                                                        complete_time: current_order.complete_time,
                                                        created_at: current_order.created_at,
                                                        updated_at: Local::now().timestamp_millis(),
                                                    };
                                                    
                                                    if let Err(e) = SalesOrderBmc::update(update_data).await {
                                                        println!("更新订单汇总字段失败: {}", e);
                                                    }
                                                }
                                            }
                                            Err(e) => {
                                                fail_count += 1;
                                                let order_id = importer.get_order_id(&order);
                                                fail_list.push(order_id.clone());
                                                let fail_info = ImportLogCreate {
                                                    import_id: order_id,
                                                    import_source: importer.get_platform_name(),
                                                    import_err: format!("创建订单条目失败: {}", e),
                                                    ..Default::default()
                                                };
                                                ImportLogBmc::create(fail_info).await?;
                                            }
                                        }
                                    } else {
                                        // 已存在相同的订单条目，不做处理
                                        no_change_count += 1;
                                        success_count += 1;
                                    }
                                }
                                Err(e) => {
                                    fail_count += 1;
                                    let order_id = importer.get_order_id(&order);
                                    fail_list.push(order_id.clone());
                                    let fail_info = ImportLogCreate {
                                        import_id: order_id,
                                        import_source: importer.get_platform_name(),
                                        import_err: format!("查询订单条目失败: {}", e),
                                        ..Default::default()
                                    };
                                    ImportLogBmc::create(fail_info).await?;
                                }
                            }
                        }
                    } else {
                        // 不存在已有条目，按照已有逻辑进行新建
                        match SalesOrderBmc::create(sales_order_create).await {
                            Ok(_) => {
                                new_count += 1;
                                success_count += 1;
                                
                                // 创建订单信息
                                let sales_order_info = importer.to_sales_order_info_create(&order);
                                // sales_order_info.order_serial = serial;
                                if let Err(e) = SalesOrderInfoBmc::create(sales_order_info).await {
                                    println!("创建订单信息失败: {}", e);
                                }
                            }
                            Err(e) => {
                                fail_count += 1;
                                let order_id = importer.get_order_id(&order);
                                fail_list.push(order_id.clone());
                                let fail_info = ImportLogCreate {
                                    import_id: order_id,
                                    import_source: importer.get_platform_name(),
                                    import_err: e.to_string(),
                                    ..Default::default()
                                };
                                ImportLogBmc::create(fail_info).await?;
                            }
                        }
                    }
                }
                Err(e) => {
                    fail_count += 1;
                    let order_id = importer.get_order_id(&order);
                    fail_list.push(order_id.clone());
                    let fail_info = ImportLogCreate {
                        import_id: order_id,
                        import_source: importer.get_platform_name(),
                        import_err: format!("查询现有订单失败: {}", e),
                        ..Default::default()
                    };
                    ImportLogBmc::create(fail_info).await?;
                }
            }
        }

        // 填充导入记录统计信息
        res.total_count = total_count;
        res.new_count = new_count;
        res.update_count = update_count;
        res.no_change_count = no_change_count;
        res.success_count = success_count;
        res.fail_count = fail_count;
        
        println!("导入统计: 总数={}, 新建={}, 更新={}, 无变化={}, 成功={}, 失败={}", 
                total_count, new_count, update_count, no_change_count, success_count, fail_count);

        Ok(res)
    }
}
