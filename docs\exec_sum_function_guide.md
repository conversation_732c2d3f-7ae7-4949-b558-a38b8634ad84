# exec_sum() 函数使用指南

## 概述

`exec_sum()` 是一个智能的数据库求和函数，能够自动判断字段类型并对数值字段进行汇总统计。该函数具有类型安全检查，只允许对可汇总的数值字段进行求和操作。

## 函数签名

```rust
pub async fn exec_sum(
    table: &str,                    // 表名
    params: Vec<WhereOptions>,      // 查询条件
    count_field: &str,              // 要求和的字段名
) -> AppResult<String>              // 返回求和结果或错误信息
```

## 核心功能

### 1. 智能字段类型检查

函数会自动检查字段是否为可汇总的数值类型：

#### ✅ 支持的字段类型
- **金额相关**: `amount`, `total_payment`, `express_fee`, `platform_fee_total`
- **价格相关**: `sales_price`, `cost_price`, `platform_fee`
- **数量相关**: `quantity`, `count`, `discount`
- **汇总相关**: `total_sales_price`, `total_cost_price`
- **时间戳**: `created_at`, `updated_at`
- **模式匹配**: 包含 `amount`, `price`, `fee`, `total`, `count`, `quantity` 关键词的字段

#### ❌ 不支持的字段类型
- 文本字段: `customer`, `address`, `platform_name`
- 状态字段: `status`, `serial`
- 其他非数值字段

### 2. 灵活的查询条件

支持使用 `Vec<WhereOptions>` 进行复杂的条件过滤：

```rust
// 单条件
let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];

// 多条件
let params = vec![
    WhereOptions::new("status".to_string(), "completed".to_string()),
    WhereOptions::new("platform_name".to_string(), "淘宝".to_string()),
];

// 时间范围
let params = vec![
    WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
    WhereOptions::new("end_date".to_string(), "2024-01-31".to_string()),
];
```

### 3. SQL 自动生成

函数会根据输入参数自动生成优化的 SQL 语句：

```sql
-- 基本求和
SELECT math::sum(total_payment) AS sum_result FROM sales_order;

-- 带条件求和
SELECT math::sum(amount) AS sum_result FROM sales_order 
WHERE status = 'completed';

-- 多条件求和
SELECT math::sum(total_payment) AS sum_result FROM sales_order 
WHERE status = 'completed' AND platform_name = '淘宝';
```

## 使用示例

### 1. 基础使用

```rust
use crate::{db::Database, services::sales_order::SalesOrderService};

// 通过 Service 层使用（推荐）
let params = vec![];
let result = SalesOrderService::sum_field(params, "total_payment").await?;

// 直接使用 Database 层
let params = vec![];
let result = Database::exec_sum("sales_order", params, "amount").await?;
```

### 2. 条件查询求和

```rust
// 统计已完成订单的总金额
let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
let result = SalesOrderService::sum_field(params, "total_payment").await?;

// 统计特定合同的订单金额
let params = vec![WhereOptions::new("contract_id".to_string(), "contract:123".to_string())];
let result = SalesOrderService::sum_field(params, "amount").await?;
```

### 3. 复杂查询

```rust
// 多条件查询
let params = vec![
    WhereOptions::new("status".to_string(), "completed".to_string()),
    WhereOptions::new("platform_name".to_string(), "淘宝".to_string()),
];
let result = SalesOrderService::sum_field(params, "total_payment").await?;

// 时间范围查询
let params = vec![
    WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
    WhereOptions::new("end_date".to_string(), "2024-01-31".to_string()),
];
let result = SalesOrderService::sum_field(params, "amount").await?;
```

## 返回值格式

### 成功情况
```rust
// 有数据时
"字段 'total_payment' 的汇总结果: 15000.50"

// 无匹配数据时
"字段 'amount' 的汇总结果: 0 (无匹配数据)"

// 字段值为空时
"字段 'express_fee' 的汇总结果: 0 (无匹配数据或字段值为空)"
```

### 错误情况
```rust
// 字段类型错误
"字段 'customer' 不是可汇总的数值类型字段。可汇总字段应包含：amount, price, fee, total, count, quantity 等关键词，或为预定义的数值字段。"

// 字段不存在
"查询结果中未找到汇总字段，可能字段 'non_existent_field' 不存在"

// 返回非数值结果
"字段 'field_name' 返回了非数值类型的结果，无法进行汇总"
```

## 应用场景

### 1. 财务统计
```rust
// 统计总收入
let result = SalesOrderService::sum_field(vec![], "total_payment").await?;

// 统计运费收入
let result = SalesOrderService::sum_field(vec![], "express_fee").await?;

// 统计平台费用支出
let result = SalesOrderService::sum_field(vec![], "platform_fee_total").await?;
```

### 2. 业务分析
```rust
// 按状态统计
let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
let result = SalesOrderService::sum_field(params, "amount").await?;

// 按平台统计
let params = vec![WhereOptions::new("platform_name".to_string(), "淘宝".to_string())];
let result = SalesOrderService::sum_field(params, "total_payment").await?;

// 按时间段统计
let params = vec![
    WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
    WhereOptions::new("end_date".to_string(), "2024-01-31".to_string()),
];
let result = SalesOrderService::sum_field(params, "amount").await?;
```

### 3. 报表生成
```rust
// 月度销售报表
let params = vec![
    WhereOptions::new("begin_date".to_string(), "2024-01-01".to_string()),
    WhereOptions::new("end_date".to_string(), "2024-01-31".to_string()),
];
let total_sales = SalesOrderService::sum_field(params.clone(), "amount").await?;
let total_fees = SalesOrderService::sum_field(params, "express_fee").await?;
```

## 扩展到其他实体

该函数设计为通用函数，可以轻松扩展到其他实体：

```rust
// 销售订单信息表
Database::exec_sum("sales_order_info", params, "total_sales_price").await?;

// 采购订单表
Database::exec_sum("purchase_order", params, "total_payment").await?;

// 库存表
Database::exec_sum("stock", params, "quantity").await?;
```

## 性能特点

1. **SQL 优化**: 使用 SurrealDB 的 `math::sum()` 函数进行数据库级汇总
2. **类型安全**: 编译时和运行时双重类型检查
3. **内存效率**: 只返回汇总结果，不加载详细数据
4. **查询优化**: 支持复杂的 WHERE 条件，充分利用数据库索引

## 错误处理

函数提供了完善的错误处理机制：

1. **字段类型验证**: 防止对非数值字段进行汇总
2. **字段存在性检查**: 检测不存在的字段
3. **结果类型验证**: 确保返回的是数值类型
4. **空结果处理**: 优雅处理无匹配数据的情况

## 总结

`exec_sum()` 函数提供了一个安全、高效、易用的数据库求和解决方案，具有以下优势：

- ✅ **类型安全**: 自动检查字段类型，防止错误操作
- ✅ **灵活查询**: 支持复杂的查询条件组合
- ✅ **性能优化**: 数据库级汇总，高效处理大数据量
- ✅ **易于使用**: 简洁的API设计，易于集成
- ✅ **错误友好**: 详细的错误信息，便于调试
- ✅ **可扩展**: 通用设计，适用于各种实体表
